package com.energyfuture.ddgc.service.fxgl.impl;


import com.energyfuture.ddgc.entity.fxgl.ModelFile;
import com.energyfuture.ddgc.mapper.fxgl.ModelFileMapper;
import com.energyfuture.ddgc.service.fxgl.MarkdownService;

import org.commonmark.node.Image;
import org.commonmark.node.Node;
import org.commonmark.parser.Parser;
import org.commonmark.renderer.html.HtmlRenderer;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class MarkdownServiceImpl implements MarkdownService {
    private static final Logger logger = LoggerFactory.getLogger(MarkdownServiceImpl.class);

    @Autowired
    private ModelFileMapper  modelFileMapper;
    String basePath = "D:/zl-code/new-ddgc/new/qsksq-master/yw-service-ddgc/src/main/resources/";

    @Override
    public String getMarkdownContent(String id) throws Exception {
        logger.debug("Fetching Markdown content for id: {}", id);

        ModelFile modelFile = modelFileMapper.findById(id);
        if (modelFile == null) {
            logger.warn("No model found for id: {}", id);
            throw new Exception("模型记录不存在：id=" + id);
        }

        String filePathStr = modelFile.getFilePath();
        if (filePathStr == null || filePathStr.trim().isEmpty()) {
            logger.warn("File path is null or empty for id: {}", id);
            throw new Exception("文件路径为空：id=" + id);
        }

        Path filePath = Paths.get(basePath, filePathStr);
        logger.debug("Constructed file path: {}", filePath);

        if (!filePath.normalize().startsWith(Paths.get(basePath).normalize())) {
            logger.error("Illegal file path detected: {}", filePath);
            throw new Exception("非法文件路径：" + filePath);
        }

        if (!Files.exists(filePath)) {
            logger.warn("File does not exist: {}", filePath);
            throw new Exception("文件不存在：" + filePath);
        }

        List<String> lines = Files.readAllLines(filePath);
        String markdownContent = lines.stream().collect(Collectors.joining("\n"));
        logger.debug("Successfully read Markdown content for id: {}", id);

        Parser parser = Parser.builder().build();
        Node document = parser.parse(markdownContent);
        HtmlRenderer renderer = HtmlRenderer.builder().build();
        return renderer.render(document);
    }

    @Override
    public String getMarkdownRawContent(String id) throws Exception {
        logger.debug("Fetching raw Markdown content for id: {}", id);

        ModelFile modelFile = modelFileMapper.findById(id);
        if (modelFile == null) {
            logger.warn("No model found for id: {}", id);
            throw new Exception("模型记录不存在：id=" + id);
        }

        String filePathStr = modelFile.getFilePath();
        if (filePathStr == null || filePathStr.trim().isEmpty()) {
            logger.warn("File path is null or empty for id: {}", id);
            throw new Exception("文件路径为空：id=" + id);
        }

        Path filePath = Paths.get(basePath, filePathStr);
        logger.debug("Constructed file path: {}", filePath);

        if (!filePath.normalize().startsWith(Paths.get(basePath).normalize())) {
            logger.error("Illegal file path detected: {}", filePath);
            throw new Exception("非法文件路径：" + filePath);
        }

        if (!Files.exists(filePath)) {
            logger.warn("File does not exist: {}", filePath);
            throw new Exception("文件不存在：" + filePath);
        }

        List<String> lines = Files.readAllLines(filePath);
        logger.debug("Successfully read raw Markdown content for id: {}", id);
        return lines.stream().collect(Collectors.joining("\n"));
    }
}
