package com.energyfuture.ddgc.service.fxgl.impl;


import com.energyfuture.ddgc.entity.fxgl.ModelFile;
import com.energyfuture.ddgc.mapper.fxgl.ModelFileMapper;
import com.energyfuture.ddgc.service.fxgl.MarkdownService;

import org.commonmark.node.Image;
import org.commonmark.node.Node;
import org.commonmark.parser.Parser;
import org.commonmark.renderer.html.HtmlRenderer;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class MarkdownServiceImpl implements MarkdownService {
    private static final Logger logger = LoggerFactory.getLogger(MarkdownServiceImpl.class);

    @Autowired
    private ModelFileMapper  modelFileMapper;
    String basePath = "D:/zl-code/new-ddgc/new/qsksq-master/yw-service-ddgc/src/main/resources/";

    @Override
    public String getMarkdownContent(String id) throws Exception {
        logger.debug("Fetching Markdown content for id: {}", id);

        ModelFile modelFile = modelFileMapper.findById(id);
        if (modelFile == null) {
            logger.warn("No model found for id: {}", id);
            throw new Exception("模型记录不存在：id=" + id);
        }

        String filePathStr = modelFile.getFilePath();
        if (filePathStr == null || filePathStr.trim().isEmpty()) {
            logger.warn("File path is null or empty for id: {}", id);
            throw new Exception("文件路径为空：id=" + id);
        }

        Path filePath = Paths.get(basePath, filePathStr);
        logger.debug("Constructed file path: {}", filePath);

        if (!filePath.normalize().startsWith(Paths.get(basePath).normalize())) {
            logger.error("Illegal file path detected: {}", filePath);
            throw new Exception("非法文件路径：" + filePath);
        }

        if (!Files.exists(filePath)) {
            logger.warn("File does not exist: {}", filePath);
            throw new Exception("文件不存在：" + filePath);
        }

        List<String> lines = Files.readAllLines(filePath);
        String markdownContent = lines.stream().collect(Collectors.joining("\n"));
        logger.debug("Successfully read Markdown content for id: {}", id);

        Parser parser = Parser.builder().build();
        Node document = parser.parse(markdownContent);
        HtmlRenderer renderer = HtmlRenderer.builder().build();
        String htmlContent = renderer.render(document);

        // 处理图片路径，将相对路径转换为可访问的URL
        return processImagePaths(htmlContent, id, filePathStr);
    }

    /**
     * 处理HTML中的图片路径，将相对路径转换为可访问的URL
     */
    private String processImagePaths(String htmlContent, String modelId, String markdownFilePath) {
        try {
            Document doc = Jsoup.parse(htmlContent);
            Elements images = doc.select("img");

            for (Element img : images) {
                String src = img.attr("src");
                if (src != null && !src.trim().isEmpty() && !src.startsWith("http")) {
                    // 处理相对路径
                    Path markdownDir = Paths.get(markdownFilePath).getParent();
                    String imagePath;
                    if (markdownDir != null) {
                        imagePath = markdownDir.resolve(src).toString().replace("\\", "/");
                    } else {
                        imagePath = src;
                    }

                    // 构建图片访问URL，对路径进行URL编码
                    String encodedImagePath = URLEncoder.encode(imagePath, StandardCharsets.UTF_8);
                    String imageUrl = "/MarkdownController/getImage?id=" + modelId + "&imagePath=" + encodedImagePath;
                    img.attr("src", imageUrl);
                    logger.debug("Converted image path: {} -> {}", src, imageUrl);
                }
            }

            return doc.body().html();
        } catch (Exception e) {
            logger.error("Error processing image paths: ", e);
            return htmlContent;
        }
    }

    @Override
    public String getMarkdownRawContent(String id) throws Exception {
        logger.debug("Fetching raw Markdown content for id: {}", id);

        ModelFile modelFile = modelFileMapper.findById(id);
        if (modelFile == null) {
            logger.warn("No model found for id: {}", id);
            throw new Exception("模型记录不存在：id=" + id);
        }

        String filePathStr = modelFile.getFilePath();
        if (filePathStr == null || filePathStr.trim().isEmpty()) {
            logger.warn("File path is null or empty for id: {}", id);
            throw new Exception("文件路径为空：id=" + id);
        }

        Path filePath = Paths.get(basePath, filePathStr);
        logger.debug("Constructed file path: {}", filePath);

        if (!filePath.normalize().startsWith(Paths.get(basePath).normalize())) {
            logger.error("Illegal file path detected: {}", filePath);
            throw new Exception("非法文件路径：" + filePath);
        }

        if (!Files.exists(filePath)) {
            logger.warn("File does not exist: {}", filePath);
            throw new Exception("文件不存在：" + filePath);
        }

        List<String> lines = Files.readAllLines(filePath);
        logger.debug("Successfully read raw Markdown content for id: {}", id);
        return lines.stream().collect(Collectors.joining("\n"));
    }

    /**
     * 获取图片文件内容
     */
    @Override
    public byte[] getImageContent(String id, String imagePath) throws Exception {
        logger.debug("Fetching image content for id: {}, imagePath: {}", id, imagePath);

        ModelFile modelFile = modelFileMapper.findById(id);
        if (modelFile == null) {
            logger.warn("No model found for id: {}", id);
            throw new Exception("模型记录不存在：id=" + id);
        }

        String filePathStr = modelFile.getFilePath();
        if (filePathStr == null || filePathStr.trim().isEmpty()) {
            logger.warn("File path is null or empty for id: {}", id);
            throw new Exception("文件路径为空：id=" + id);
        }

        // 获取Markdown文件所在目录
        Path markdownDir = Paths.get(basePath, filePathStr).getParent();
        if (markdownDir == null) {
            markdownDir = Paths.get(basePath);
        }

        // 构建图片文件路径
        Path imageFilePath = markdownDir.resolve(imagePath).normalize();
        logger.debug("Constructed image file path: {}", imageFilePath);

        // 安全检查：确保图片文件在允许的目录范围内
        if (!imageFilePath.startsWith(Paths.get(basePath).normalize())) {
            logger.error("Illegal image file path detected: {}", imageFilePath);
            throw new Exception("非法图片文件路径：" + imageFilePath);
        }

        if (!Files.exists(imageFilePath)) {
            logger.warn("Image file does not exist: {}", imageFilePath);
            throw new Exception("图片文件不存在：" + imageFilePath);
        }

        // 检查是否为图片文件
        String fileName = imageFilePath.getFileName().toString().toLowerCase();
        if (!fileName.endsWith(".png") && !fileName.endsWith(".jpg") &&
            !fileName.endsWith(".jpeg") && !fileName.endsWith(".gif") &&
            !fileName.endsWith(".bmp") && !fileName.endsWith(".svg")) {
            logger.warn("Not an image file: {}", imageFilePath);
            throw new Exception("不是有效的图片文件：" + imageFilePath);
        }

        byte[] imageBytes = Files.readAllBytes(imageFilePath);
        logger.debug("Successfully read image content for id: {}, size: {} bytes", id, imageBytes.length);
        return imageBytes;
    }
}
