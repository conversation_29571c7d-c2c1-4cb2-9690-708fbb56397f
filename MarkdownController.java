package com.energyfuture.ddgc.controller.fxgl;

import com.energyfuture.ddgc.service.fxgl.MarkdownService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Markdown控制器
 */
@RestController
@RequestMapping("/ddgc/MarkdownController")
public class MarkdownController {
    
    private static final Logger logger = LoggerFactory.getLogger(MarkdownController.class);
    
    @Autowired
    private MarkdownService markdownService;
    
    /**
     * 获取Markdown内容并转换为HTML
     */
    @GetMapping("/getMarkdown")
    public ResponseEntity<Map<String, Object>> getMarkdown(@RequestParam String id) {
        Map<String, Object> result = new HashMap<>();
        try {
            String htmlContent = markdownService.getMarkdownContent(id);
            result.put("code", 200);
            result.put("data", htmlContent);
            result.put("msg", "获取成功");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("获取Markdown内容失败: ", e);
            result.put("code", 500);
            result.put("data", null);
            result.put("msg", "获取失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
    
    /**
     * 获取原始Markdown内容
     */
    @GetMapping("/getMarkdownRaw")
    public ResponseEntity<Map<String, Object>> getMarkdownRaw(@RequestParam String id) {
        Map<String, Object> result = new HashMap<>();
        try {
            String rawContent = markdownService.getMarkdownRawContent(id);
            result.put("code", 200);
            result.put("data", rawContent);
            result.put("msg", "获取成功");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("获取原始Markdown内容失败: ", e);
            result.put("code", 500);
            result.put("data", null);
            result.put("msg", "获取失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
    
    /**
     * 获取图片资源
     */
    @GetMapping("/getImage")
    public ResponseEntity<byte[]> getImage(@RequestParam String id, @RequestParam String imagePath) {
        try {
            byte[] imageBytes = markdownService.getImageContent(id, imagePath);
            
            // 根据文件扩展名设置Content-Type
            String contentType = getContentType(imagePath);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(contentType));
            headers.setContentLength(imageBytes.length);
            
            return new ResponseEntity<>(imageBytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            logger.error("获取图片失败: ", e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }
    }
    
    /**
     * 根据文件扩展名获取Content-Type
     */
    private String getContentType(String imagePath) {
        String fileName = imagePath.toLowerCase();
        if (fileName.endsWith(".png")) {
            return "image/png";
        } else if (fileName.endsWith(".jpg") || fileName.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (fileName.endsWith(".gif")) {
            return "image/gif";
        } else if (fileName.endsWith(".bmp")) {
            return "image/bmp";
        } else if (fileName.endsWith(".svg")) {
            return "image/svg+xml";
        } else {
            return "image/png"; // 默认
        }
    }
}
