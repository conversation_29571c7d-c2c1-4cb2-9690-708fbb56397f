
package com.energyfuture.ddgc.controller.fxgl;

import com.energyfuture.ddgc.service.fxgl.MarkdownService;
import com.energyfuture.ddgc.common.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/MarkdownController")
public class MarkdownController {

    @Autowired
    private MarkdownService markdownService;

    @GetMapping("/getMarkdown")
    public Result<String> getMarkdownContent(@RequestParam String id) {
        try {
            String htmlContent = markdownService.getMarkdownContent(id);
            return Result.success(htmlContent);
        } catch (Exception e) {
            return Result.error(500, "错误：" + e.getMessage());
        }
    }

    @GetMapping("/getMarkdownRaw")
    public Result<String> getMarkdownRawContent(@RequestParam String id) {
        try {
            String rawContent = markdownService.getMarkdownRawContent(id);
            return Result.success(rawContent);
        } catch (Exception e) {
            return Result.error(500, "错误：" + e.getMessage());
        }
    }
}


