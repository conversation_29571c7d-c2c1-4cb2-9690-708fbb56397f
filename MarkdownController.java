
package com.energyfuture.ddgc.controller.fxgl;

import com.energyfuture.ddgc.service.fxgl.MarkdownService;
import com.energyfuture.ddgc.common.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/MarkdownController")
public class MarkdownController {

    @Autowired
    private MarkdownService markdownService;

    @GetMapping("/getMarkdown")
    public Result<String> getMarkdownContent(@RequestParam String id) {
        try {
            String htmlContent = markdownService.getMarkdownContent(id);
            return Result.success(htmlContent);
        } catch (Exception e) {
            return Result.error(500, "错误：" + e.getMessage());
        }
    }

    @GetMapping("/getMarkdownRaw")
    public Result<String> getMarkdownRawContent(@RequestParam String id) {
        try {
            String rawContent = markdownService.getMarkdownRawContent(id);
            return Result.success(rawContent);
        } catch (Exception e) {
            return Result.error(500, "错误：" + e.getMessage());
        }
    }

    @GetMapping("/getImage")
    public ResponseEntity<byte[]> getImage(@RequestParam String id, @RequestParam String imagePath) {
        try {
            byte[] imageBytes = markdownService.getImageContent(id, imagePath);

            // 根据文件扩展名设置Content-Type
            String contentType = getContentType(imagePath);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(contentType));
            headers.setContentLength(imageBytes.length);

            return new ResponseEntity<>(imageBytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }
    }

    private String getContentType(String imagePath) {
        String fileName = imagePath.toLowerCase();
        if (fileName.endsWith(".png")) {
            return "image/png";
        } else if (fileName.endsWith(".jpg") || fileName.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (fileName.endsWith(".gif")) {
            return "image/gif";
        } else if (fileName.endsWith(".bmp")) {
            return "image/bmp";
        } else if (fileName.endsWith(".svg")) {
            return "image/svg+xml";
        } else {
            return "image/png";
        }
    }
}


