package com.energyfuture.ddgc.service.fxgl;

/**
 * Markdown服务接口
 */
public interface MarkdownService {

    /**
     * 获取Markdown内容并转换为HTML
     * @param id 模型文件ID
     * @return HTML内容
     * @throws Exception 异常
     */
    String getMarkdownContent(String id) throws Exception;

    /**
     * 获取原始Markdown内容
     * @param id 模型文件ID
     * @return 原始Markdown内容
     * @throws Exception 异常
     */
    String getMarkdownRawContent(String id) throws Exception;

    /**
     * 获取图片文件内容
     * @param imagePath 图片相对路径
     * @return 图片文件字节数组
     * @throws Exception 异常
     */
    byte[] getImageContent(String imagePath) throws Exception;
}
