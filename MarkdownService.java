package com.energyfuture.ddgc.service.fxgl;

/**
 * Markdown服务接口
 */
public interface MarkdownService {

    /**
     * 获取Markdown内容并转换为HTML
     * @param id 模型文件ID
     * @return HTML内容
     * @throws Exception 异常
     */
    String getMarkdownContent(String id) throws Exception;

    /**
     * 获取Markdown内容并转换为HTML（带基础URL）
     * @param id 模型文件ID
     * @param baseUrl 基础URL
     * @return HTML内容
     * @throws Exception 异常
     */
    String getMarkdownContent(String id, String baseUrl) throws Exception;

    /**
     * 获取原始Markdown内容
     * @param id 模型文件ID
     * @return 原始Markdown内容
     * @throws Exception 异常
     */
    String getMarkdownRawContent(String id) throws Exception;
}
