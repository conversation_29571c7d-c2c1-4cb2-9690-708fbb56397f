<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown 预览</title>
    <link rel="stylesheet" href="../../../lib/ol3/ol.css">
    <link rel="stylesheet" href="../../../lib/layui/css/layui.css">
    <link rel="stylesheet" href="../../css/ddgc.css">
    <script src="../../../lib/jquery/jquery-2.1.4.min.js"></script>
    <script src="../../../lib/ol3/ol.js"></script>
    <script src="../../../lib/layui/layui.all.js"></script>
    <script src="../../../config/config.js"></script>
    <script src="../../../js/common/common.js"></script>
    <script src="../../../ddgc/js/chart.min.js"></script>
    <link rel="stylesheet" href="../../css/github-markdown.min.css">
    <style>
        .layui-container {
            padding: 10px;
        }

        .layui-btn-search:hover {
            background-color: #388e8e;
        }

        .anniu {
            height: 2.8vh;
            line-height: 2.8vh;
            margin-left: 0.4vw;
        }

        .layui-form-label {
            line-height: 4px !important;
            padding: 9px 5px;
        }

        .layui-input, .layui-select, .layui-textarea {
            height: 3vh !important;
        }

        .layui-card-header {
            height: 34px;
            line-height: 30px !important;
            margin-top: 10px;
            padding: 0 !important;
        }

        .layui-card-body {
            padding: 0px 15px !important;
        }

        .layui-form-item .layui-inline {
            margin-bottom: 0 !important;
            margin-right: 0 !important;
        }

        .layui-form-item .layui-input-inline {
            float: left;
            width: 200px;
            margin-right: 10px;
        }

        .markdown-body {
            box-sizing: border-box;
            min-width: 200px;
            max-width: 980px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            background-color: #fff;
            min-height: calc(108vh - 150px);
        }

        @media (max-width: 767px) {
            .markdown-body {
                padding: 15px;
            }
        }

        .layui-row {
            margin: 0 14px;
        }

        .empty-content {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            min-height: 300px;
            color: #999;
        }
        .download-btn {
            margin-left: 10px;
            height: 3vh !important;
            line-height: 3vh !important;
            padding: 0 15px !important;
        }
    </style>
</head>
<body class="layui-layout-body" style="overflow-y: auto">
<div class="layui-card" style="margin: 0 14px">
    <div class="layui-form layui-card-header">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">训练名称</label>
                <div class="layui-input-inline">
                    <select id="trainName-filter" name="trainName" lay-filter="trainName-filter">
                        <option value="">请选择训练名称</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <button type="button" class="layui-btn layui-btn-normal download-btn" id="downloadBtn">
                    <i class="layui-icon layui-icon-download-circle"></i> 下载MD
                </button>
            </div>
        </div>
    </div>
</div>
<div class="layui-row">
    <div class="layui-col-md12">
        <div class="layui-card">
            <div class="layui-card-header">训练报告</div>
            <div class="layui-card-body">
                <div class="markdown-body" id="contentArea">
                    <div class="empty-content">请选择模型名称以预览 Markdown 内容。</div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    layui.use(['form', 'layer', 'jquery'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;

        // 当前选中的训练名称（用于下载文件名）
        var currentTrainName = '';
        // 当前选中的 id
        var currentTrainId = '';

        function getCookie(name) {
            var value = "; " + document.cookie;
            var parts = value.split("; " + name + "=");
            if (parts.length === 2) return parts.pop().split(";").shift();
            return "";
        }

        function showEmptyContent(message) {
            $('#contentArea').html(`<div class="empty-content">${message}</div>`);
        }

        function downloadMarkdown(trainId, trainName) {
            if (!trainId) {
                layer.msg('请先选择训练名称！', {icon: 2});
                return;
            }

            var loadingIndex = layer.load(1, {shade: [0.1, '#fff']});

            $.ajax({
                url: commonUrl.baseUrl + 'ddgc/MarkdownController/getMarkdownRaw',
                headers: {Authorization: getCookie("token") || ""},
                method: 'GET',
                data: {id: trainId},
                success: function (res) {
                    layer.close(loadingIndex);
                    if (res.code === 200 && res.data) {
                        var blob = new Blob([res.data], {type: 'text/markdown;charset=utf-8'});
                        var url = window.URL.createObjectURL(blob);
                        var a = document.createElement('a');
                        a.href = url;
                        a.download = trainName + '.md';
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        window.URL.revokeObjectURL(url);
                        layer.msg('下载成功！', {icon: 1});
                    } else {
                        showEmptyContent('没有找到对应文件');
                    }
                },
                error: function (xhr) {
                    layer.close(loadingIndex);
                    showEmptyContent('没有找到对应文件');
                    layer.msg('下载失败，请检查网络！', {icon: 2});
                }
            });
        }

        function loadTrainData(trainId, trainName) {
            currentTrainId = trainId;
            currentTrainName = trainName;
            if (!trainId) {
                showEmptyContent('请选择模型名称以预览 Markdown 内容。');
                return;
            }

            $.ajax({
                url: commonUrl.baseUrl + 'ddgc/MarkdownController/getMarkdown',
                headers: {Authorization: getCookie("token") || ""},
                method: 'GET',
                data: {id: trainId},
                success: function (res) {
                    if (res.code === 200) {
                        $('#contentArea').html(res.data);
                    } else {
                        showEmptyContent('没有找到对应文件');
                    }
                },
                error: function (xhr) {
                    showEmptyContent('没有找到对应文件');
                    layer.msg('请求失败，请检查网络！', {icon: 2});
                }
            });
        }

        $.ajax({
            url: commonUrl.baseUrl + 'ddgc/TrainController/list',
            headers: {Authorization: getCookie("token") || ""},
            method: 'GET',
            success: function (res) {
                if (res.code === 200 && res.data && res.data.length > 0) {
                    var select = $('#trainName-filter');
                    res.data.forEach(function (item) {
                        select.append(`<option value="${item.id}" data-train-name="${item.trainName}">${item.trainName}</option>`);
                    });
                    form.render('select');

                    // 默认选中第一个
                    select.val(res.data[0].id);
                    form.render('select');

                    loadTrainData(res.data[0].id, res.data[0].trainName);
                } else {
                    showEmptyContent('没有找到对应文件');
                    layer.msg(res.msg || '获取训练名称失败或无数据', {icon: 2});
                }
            },
            error: function (xhr) {
                showEmptyContent('没有找到对应文件');
                layer.msg('网络错误，请检查网络或 token 是否有效', {icon: 2});
                console.error('AJAX error:', xhr);
            }
        });

        form.on('select(trainName-filter)', function (data) {
            var trainId = data.value;
            var trainName = $(data.elem).find('option:selected').data('train-name') || '';
            loadTrainData(trainId, trainName);
        });

        $('#downloadBtn').on('click', function () {
            downloadMarkdown(currentTrainId, currentTrainName);
        });
    });
</script>
</body>
</html>
